/**
 * 场景管理路由
 * 提供12个PRD定义的训练场景
 */

const express = require('express');
// 动态导入数据库配置
let dbConfig;
try {
  if (process.env.TCB_ENV_ID && process.env.TCB_SECRET_ID && process.env.TCB_SECRET_KEY) {
    dbConfig = require('../../cloudbase/database/db');
  } else {
    dbConfig = require('../../cloudbase/database/db-mock');
  }
} catch (error) {
  dbConfig = require('../../cloudbase/database/db-mock');
}
const { db, COLLECTIONS } = dbConfig;
const { authMiddleware } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取所有场景列表（学生端）
 * GET /api/scenarios
 */
router.get('/', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;

    // 获取所有激活的场景
    const scenariosResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        is_active: true
      })
      .orderBy('id', 'asc')
      .get();

    let scenarios = scenariosResult.data;

    // 如果是学生，获取学习进度
    if (userRole === 'student') {
      const progressResult = await db
        .collection(COLLECTIONS.PROGRESS)
        .where({
          student_id: userId
        })
        .get();

      const progressMap = {};
      progressResult.data.forEach(p => {
        progressMap[p.scenario_id] = p;
      });

      // 合并场景和进度信息
      scenarios = scenarios.map(scenario => ({
        ...scenario,
        progress: progressMap[scenario.id] || {
          status: 'not_started',
          progress: 0,
          score: null,
          time_spent: 0
        }
      }));
    }

    // 按模块分组
    const groupedScenarios = {
      module1: scenarios.filter(s => s.module === 1),
      module2: scenarios.filter(s => s.module === 2), 
      module3: scenarios.filter(s => s.module === 3)
    };

    res.json({
      success: true,
      data: {
        scenarios: groupedScenarios,
        total: scenarios.length,
        modules: [
          { id: 1, name: '基础操作', count: groupedScenarios.module1.length },
          { id: 2, name: '流程整合', count: groupedScenarios.module2.length },
          { id: 3, name: '智能应用', count: groupedScenarios.module3.length }
        ]
      }
    });

  } catch (error) {
    console.error('获取场景列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取场景列表失败'
    });
  }
});

/**
 * 获取单个场景详情
 * GET /api/scenarios/:id
 */
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const scenarioId = parseInt(req.params.id);
    const userId = req.user.userId;
    const userRole = req.user.role;

    // 获取场景信息
    const scenarioResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        id: scenarioId,
        is_active: true
      })
      .get();

    if (!scenarioResult.data.length) {
      return res.status(404).json({
        success: false,
        message: '场景不存在'
      });
    }

    let scenario = scenarioResult.data[0];

    // 如果是学生，获取学习进度
    if (userRole === 'student') {
      const progressResult = await db
        .collection(COLLECTIONS.PROGRESS)
        .where({
          student_id: userId,
          scenario_id: scenarioId
        })
        .get();

      if (progressResult.data.length > 0) {
        scenario.progress = progressResult.data[0];
      } else {
        scenario.progress = {
          status: 'not_started',
          progress: 0,
          score: null,
          time_spent: 0,
          first_access_time: null,
          last_access_time: null
        };
      }
    }

    res.json({
      success: true,
      data: scenario
    });

  } catch (error) {
    console.error('获取场景详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取场景详情失败'
    });
  }
});

/**
 * 开始场景学习（记录首次访问）
 * POST /api/scenarios/:id/start
 */
router.post('/:id/start', authMiddleware, async (req, res) => {
  try {
    const scenarioId = parseInt(req.params.id);
    const userId = req.user.userId;

    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以开始学习场景'
      });
    }

    // 检查场景是否存在
    const scenarioResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        id: scenarioId,
        is_active: true
      })
      .get();

    if (!scenarioResult.data.length) {
      return res.status(404).json({
        success: false,
        message: '场景不存在'
      });
    }

    const scenario = scenarioResult.data[0];

    // 检查是否已有学习记录
    const existingResult = await db
      .collection(COLLECTIONS.PROGRESS)
      .where({
        student_id: userId,
        scenario_id: scenarioId
      })
      .get();

    const now = new Date();

    if (existingResult.data.length === 0) {
      // 创建新的学习记录
      await db.collection(COLLECTIONS.PROGRESS).add({
        student_id: userId,
        scenario_id: scenarioId,
        status: 'in_progress',
        progress: 0,
        time_spent: 0,
        score: null,
        first_access_time: now,
        last_access_time: now,
        completed_at: null,
        created_at: now,
        updated_at: now
      });

      console.log(`学生 ${req.user.username} 开始学习场景${scenarioId}: ${scenario.title}`);
    } else {
      // 更新最后访问时间
      await db
        .collection(COLLECTIONS.PROGRESS)
        .doc(existingResult.data[0]._id)
        .update({
          last_access_time: now,
          updated_at: now,
          ...(existingResult.data[0].status === 'not_started' && { status: 'in_progress' })
        });
    }

    res.json({
      success: true,
      message: '场景学习已开始',
      data: {
        scenario_url: `/scenarios/${scenario.code}.html`,
        scenario_title: scenario.title
      }
    });

  } catch (error) {
    console.error('开始场景学习失败:', error);
    res.status(500).json({
      success: false,
      message: '开始场景学习失败'
    });
  }
});

/**
 * 更新学习进度
 * PUT /api/scenarios/:id/progress
 */
router.put('/:id/progress', authMiddleware, async (req, res) => {
  try {
    const scenarioId = parseInt(req.params.id);
    const userId = req.user.userId;
    const { progress, timeSpent } = req.body;

    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以更新学习进度'
      });
    }

    // 验证进度值
    if (progress < 0 || progress > 100) {
      return res.status(400).json({
        success: false,
        message: '进度值必须在0-100之间'
      });
    }

    // 查找学习记录
    const progressResult = await db
      .collection(COLLECTIONS.PROGRESS)
      .where({
        student_id: userId,
        scenario_id: scenarioId
      })
      .get();

    if (!progressResult.data.length) {
      return res.status(404).json({
        success: false,
        message: '学习记录不存在，请先开始学习'
      });
    }

    const updateData = {
      progress: progress,
      last_access_time: new Date(),
      updated_at: new Date()
    };

    if (timeSpent !== undefined) {
      updateData.time_spent = timeSpent;
    }

    // 更新学习记录
    await db
      .collection(COLLECTIONS.PROGRESS)
      .doc(progressResult.data[0]._id)
      .update(updateData);

    res.json({
      success: true,
      message: '学习进度已更新'
    });

  } catch (error) {
    console.error('更新学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '更新学习进度失败'
    });
  }
});

/**
 * 验证场景任务完成情况
 * POST /api/scenarios/:id/validate
 */
router.post('/:id/validate', authMiddleware, async (req, res) => {
  try {
    const scenarioId = parseInt(req.params.id);
    const userId = req.user.userId;
    const validationData = req.body;

    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以提交任务验证'
      });
    }

    // 获取场景信息
    const scenarioResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        id: scenarioId,
        is_active: true
      })
      .get();

    if (!scenarioResult.data.length) {
      return res.status(404).json({
        success: false,
        message: '场景不存在'
      });
    }

    const scenario = scenarioResult.data[0];
    let isValid = false;
    let score = 0;
    let feedback = '';

    // 根据不同场景ID进行验证
    switch (scenarioId) {
      case 1: // 银行流水查询下载
        isValid = validateScenario1(validationData);
        break;
      case 2: // 批量开具电子发票
        isValid = validateScenario2(validationData);
        break;
      case 3: // 固定资产卡片信息核对
        isValid = validateScenario3(validationData);
        break;
      case 4: // 税务申报期查询
        isValid = validateScenario4(validationData);
        break;
      case 5: // 应收账款对账与核销
        isValid = validateScenario5(validationData);
        break;
      case 6: // 增值税进项税发票认证
        isValid = validateScenario6(validationData);
        break;
      case 7: // 月度工资条数据计算与生成
        isValid = validateScenario7(validationData);
        break;
      case 8: // 财务报表数据自动汇总
        isValid = validateScenario8(validationData);
        break;
      case 9: // 供应商发票批量验真与入账 (OCR)
        isValid = validateScenario9(validationData);
        break;
      case 10: // 员工差旅费报销智能初审 (人机协作)
        isValid = validateScenario10(validationData);
        break;
      case 11: // 合同关键信息提取与印花税计算申报 (OCR)
        isValid = validateScenario11(validationData);
        break;
      case 12: // 自动生成总账科目余额调节表
        isValid = validateScenario12(validationData);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: '暂不支持该场景的验证'
        });
    }

    if (isValid) {
      score = 100;
      feedback = '恭喜！您已成功完成所有必需的操作步骤。';
      
      // 更新学习记录为已完成
      const progressResult = await db
        .collection(COLLECTIONS.PROGRESS)
        .where({
          student_id: userId,
          scenario_id: scenarioId
        })
        .get();

      if (progressResult.data.length > 0) {
        await db
          .collection(COLLECTIONS.PROGRESS)
          .doc(progressResult.data[0]._id)
          .update({
            status: 'completed',
            progress: 100,
            score: score,
            completed_at: new Date(),
            last_access_time: new Date(),
            updated_at: new Date(),
            validation_data: validationData
          });

        console.log(`学生 ${req.user.username} 完成场景${scenarioId}: ${scenario.title}，得分: ${score}`);
      }
    } else {
      feedback = '任务验证未通过，请检查是否完成了所有必需的操作步骤。';
    }

    res.json({
      success: true,
      valid: isValid,
      score: score,
      feedback: feedback,
      data: {
        scenario_title: scenario.title,
        completion_time: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('场景验证失败:', error);
    res.status(500).json({
      success: false,
      message: '场景验证失败',
      valid: false
    });
  }
});

// 场景1验证逻辑：银行流水查询下载
function validateScenario1(data) {
  const requiredSteps = ['login', 'query', 'download', 'save'];
  const requiredData = ['accountNumber', 'dateRange', 'transactionCount'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查是否包含必需的数据
  const dataComplete = requiredData.every(field => 
    data[field] !== undefined && data[field] !== null
  );
  
  return stepsCompleted && dataComplete && data.transactionCount >= 3;
}

// 场景2验证逻辑：批量开具电子发票
function validateScenario2(data) {
  const requiredSteps = ['upload', 'parse', 'generate', 'complete'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查是否处理了足够的发票
  const invoiceCount = data.invoiceCount || 0;
  
  return stepsCompleted && invoiceCount >= 3;
}

// 场景3验证逻辑：固定资产卡片信息核对
function validateScenario3(data) {
  const requiredSteps = ['upload', 'query', 'compare', 'export'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查是否处理了足够的资产记录
  const assetCount = data.assetCount || 0;
  const hasMatchResults = data.matchResults && 
    typeof data.matchResults.perfect === 'number' &&
    typeof data.matchResults.partial === 'number' &&
    typeof data.matchResults.notfound === 'number';
  
  return stepsCompleted && assetCount >= 3 && hasMatchResults;
}

// 场景4验证逻辑：税务申报期查询
function validateScenario4(data) {
  const requiredSteps = ['login', 'calendar', 'status', 'report'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查是否获取了申报信息
  const hasCalendarData = data.calendarData && data.calendarData.length > 0;
  const hasStatusData = data.statusData && data.statusData.length > 0;
  
  return stepsCompleted && hasCalendarData && hasStatusData;
}

// 场景5验证逻辑：应收账款对账与核销
function validateScenario5(data) {
  const requiredSteps = ['upload_sales', 'upload_bank', 'matching', 'reconciliation'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查数据处理量
  const hasValidDataCounts = data.salesDataCount >= 3 && data.bankDataCount >= 3;
  
  // 检查匹配结果
  const hasMatchingResults = data.matchingResults && 
    typeof data.matchingResults.total === 'number' &&
    typeof data.matchingResults.matched === 'number' &&
    typeof data.matchingResults.reconciled === 'number';
  
  // 检查核销率（至少50%的记录成功核销）
  const reconciliationRate = hasMatchingResults ? 
    data.matchingResults.reconciled / data.matchingResults.total : 0;
  
  return stepsCompleted && hasValidDataCounts && hasMatchingResults && 
    reconciliationRate >= 0.5 && data.taskCompleted;
}

// 场景6验证逻辑：增值税进项税发票认证
function validateScenario6(data) {
  const requiredSteps = ['upload_csv', 'navigation', 'authentication'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查数据处理量
  const hasValidInvoiceCount = data.invoiceCount >= 3;
  const hasSelectedInvoices = data.selectedCount >= 1;
  const hasAuthenticated = data.authenticatedCount >= 1;
  
  // 检查认证成功率（至少70%）
  const successRate = data.successRate || 0;
  
  return stepsCompleted && hasValidInvoiceCount && hasSelectedInvoices && 
    hasAuthenticated && successRate >= 0.7 && data.taskCompleted;
}

// 场景7验证逻辑：月度工资条数据计算与生成
function validateScenario7(data) {
  const requiredSteps = ['upload_attendance', 'upload_performance', 'calculation', 'generation'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查数据处理量
  const hasValidDataCounts = data.attendanceDataCount >= 3 && data.performanceDataCount >= 3;
  const hasCalculatedSalaries = data.calculatedSalaries >= 3;
  const hasGeneratedPayslips = data.generatedPayslips >= 3;
  
  // 检查工资总额合理性（大于0）
  const hasTotalPayroll = data.totalPayroll > 0;
  
  return stepsCompleted && hasValidDataCounts && hasCalculatedSalaries && 
    hasGeneratedPayslips && hasTotalPayroll && data.taskCompleted;
}

// 场景8验证逻辑：财务报表数据自动汇总
function validateScenario8(data) {
  const requiredSteps = ['connect_companies', 'extract_data', 'consolidate_data', 'generate_report'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查连接的公司数量
  const hasConnectedCompanies = data.connectedCompanies >= 3;
  const hasExtractedData = data.extractedDataCount >= 3;
  
  // 检查合并数据的完整性
  const hasConsolidatedData = data.consolidatedData && 
    typeof data.consolidatedData.totalAssets === 'number' &&
    typeof data.consolidatedData.totalRevenue === 'number' &&
    typeof data.consolidatedData.totalNetProfit === 'number';
  
  // 检查数据合理性（总资产大于总收入，总收入大于净利润）
  const dataRationality = hasConsolidatedData && 
    data.consolidatedData.totalAssets > data.consolidatedData.totalRevenue &&
    data.consolidatedData.totalRevenue > data.consolidatedData.totalNetProfit &&
    data.consolidatedData.totalNetProfit > 0;
  
  return stepsCompleted && hasConnectedCompanies && hasExtractedData && 
    hasConsolidatedData && dataRationality && data.reportGenerated && data.taskCompleted;
}

// 场景9验证逻辑：供应商发票批量验真与入账 (OCR)
function validateScenario9(data) {
  const requiredSteps = ['upload', 'ocr', 'verification', 'entries'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查OCR处理量和质量
  const hasValidUploadCount = data.uploadedFileCount >= 2;
  const hasValidOCRResults = data.ocrResultCount >= 1;
  const hasValidVerification = data.verifiedInvoiceCount >= 1;
  const hasValidEntries = data.generatedEntriesCount >= 1;
  
  // 检查OCR平均置信度（至少70%）
  const hasGoodConfidence = data.averageConfidence >= 0.7;
  
  // 检查发票总金额合理性
  const hasValidTotalAmount = data.totalInvoiceAmount > 0;
  
  return stepsCompleted && hasValidUploadCount && hasValidOCRResults && 
    hasValidVerification && hasValidEntries && hasGoodConfidence && 
    hasValidTotalAmount && data.taskCompleted;
}

// 场景10验证逻辑：员工差旅费报销智能初审 (人机协作)
function validateScenario10(data) {
  const requiredSteps = ['fetch', 'audit', 'review'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查处理的报销单数量
  const hasValidExpenseCount = data.totalExpenses >= 3;
  
  // 检查智能审核效果
  const hasAutoApproval = data.autoApprovedCount >= 0;
  const hasManualReview = data.manualReviewCount >= 0;
  const hasRejections = data.rejectedCount >= 0;
  
  // 检查处理准确率（至少80%）
  const hasGoodAccuracy = data.processingAccuracy >= 0.8;
  
  // 检查是否完成了人机协作
  const hasCollaboration = data.humanMachineCollaboration !== undefined;
  
  // 检查总数平衡（所有分类加起来等于总数）
  const totalProcessed = data.autoApprovedCount + data.manualReviewCount + data.rejectedCount;
  const isBalanced = totalProcessed === data.totalExpenses;
  
  return stepsCompleted && hasValidExpenseCount && hasAutoApproval !== false && 
    hasManualReview !== false && hasRejections !== false && hasGoodAccuracy && 
    hasCollaboration && isBalanced && data.taskCompleted;
}

// 场景11验证逻辑：合同关键信息提取与印花税计算申报 (OCR)
function validateScenario11(data) {
  const requiredSteps = ['upload', 'ocr', 'calculation', 'declaration'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查文件处理
  const hasValidUploadCount = data.uploadedFileCount >= 1;
  const hasValidExtractionCount = data.extractedContractCount >= 1;
  const hasValidCalculationCount = data.calculatedTaxCount >= 1;
  
  // 检查合同金额和税额
  const hasValidContractAmount = data.totalContractAmount > 0;
  const hasValidTaxAmount = data.totalTaxAmount > 0;
  
  // 检查OCR识别质量（平均置信度至少70%）
  const hasGoodConfidence = data.averageConfidence >= 0.7;
  
  // 检查申报表生成
  const hasDeclaration = data.declarationGenerated === true;
  
  // 检查合同类型多样性（至少包含一种合同类型）
  const hasContractTypes = data.contractTypes && data.contractTypes.length >= 1;
  
  return stepsCompleted && hasValidUploadCount && hasValidExtractionCount && 
    hasValidCalculationCount && hasValidContractAmount && hasValidTaxAmount && 
    hasGoodConfidence && hasDeclaration && hasContractTypes && data.taskCompleted;
}

// 场景12验证逻辑：自动生成总账科目余额调节表
function validateScenario12(data) {
  const requiredSteps = ['data_source', 'data_display', 'reconciliation', 'validation'];
  
  // 检查是否完成了所有步骤
  const stepsCompleted = requiredSteps.every(step => 
    data.completedSteps && data.completedSteps.includes(step)
  );
  
  // 检查数据源使用（至少使用2个数据源）
  const hasValidDataSources = data.dataSourcesUsed >= 2;
  
  // 检查账户余额
  const hasBankBalance = data.bankBalance > 0;
  const hasLedgerBalance = data.ledgerBalance > 0;
  const hasAdjustedBalance = data.adjustedBalance > 0;
  
  // 检查调节表状态
  const isReconciled = data.isReconciled !== undefined;
  
  // 检查未达账项处理（至少3个未达账项）
  const hasValidOutstandingItems = data.outstandingItemsCount >= 3;
  
  // 检查差异分析
  const hasDifferenceAnalysis = data.differencesFound >= 0;
  
  // 检查调节准确率（至少80%）
  const hasGoodAccuracy = data.reconciliationAccuracy >= 0.8;
  
  return stepsCompleted && hasValidDataSources && hasBankBalance && 
    hasLedgerBalance && hasAdjustedBalance && isReconciled && 
    hasValidOutstandingItems && hasDifferenceAnalysis !== false && 
    hasGoodAccuracy && data.taskCompleted;
}

module.exports = router;