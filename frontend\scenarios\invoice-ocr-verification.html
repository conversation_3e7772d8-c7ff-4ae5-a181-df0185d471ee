<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景9：供应商发票批量验真与入账 (OCR) - RPA财务机器人实训平台</title>
    
    <!-- 样式引入 -->
    <style>
        /* 薄暮天空配色系统 */
        :root {
            --primary-gradient: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            --primary-color: #7673FF;
            --primary-light: rgba(118, 115, 255, 0.15);
            --text-primary: #2c3e50;
            --text-secondary: #8D99AE;
            --bg-color: #F9FAFB;
            --card-bg: #FFFFFF;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 10px 15px -3px rgba(118, 115, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .header {
            background: var(--primary-gradient);
            padding: 1rem 2rem;
            color: white;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scenario-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .scenario-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* 进度条 */
        .progress-container {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: #E5E7EB;
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .step {
            text-align: center;
            padding: 0.5rem;
        }

        .step-circle {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #E5E7EB;
            color: #6B7280;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 操作面板 */
        .operation-panel {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .operation-panel:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .panel-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--primary-color);
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 0.75rem;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FAFBFC;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: scale(1.02);
        }

        .upload-icon {
            width: 3rem;
            height: 3rem;
            fill: var(--text-secondary);
            margin: 0 auto 1rem;
        }

        .upload-text {
            font-size: 1.125rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .upload-hint {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* 文件列表 */
        .file-list {
            margin-top: 1.5rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #F8FAFC;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .file-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--primary-color);
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .file-size {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .file-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* OCR 结果展示 */
        .ocr-results {
            margin-top: 2rem;
        }

        .result-card {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .invoice-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .confidence-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .confidence-high {
            background: #DCFCE7;
            color: #166534;
        }

        .confidence-medium {
            background: #FEF3C7;
            color: #92400E;
        }

        .confidence-low {
            background: #FEE2E2;
            color: #991B1B;
        }

        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .result-field {
            display: flex;
            flex-direction: column;
        }

        .field-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .field-value {
            font-weight: 500;
            color: var(--text-primary);
        }

        /* 验证状态 */
        .verification-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
        }

        .status-success {
            background: #DCFCE7;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .status-warning {
            background: #FEF3C7;
            color: #92400E;
            border: 1px solid #FDE68A;
        }

        .status-error {
            background: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        /* 会计分录 */
        .accounting-entry {
            background: #F8FAFC;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
        }

        .entry-table {
            width: 100%;
            border-collapse: collapse;
        }

        .entry-table th,
        .entry-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .entry-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid #E5E7EB;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 提示消息 */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #DCFCE7;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .alert-info {
            background: #DBEAFE;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .alert-warning {
            background: #FEF3C7;
            color: #92400E;
            border: 1px solid #FDE68A;
        }

        .alert-error {
            background: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .progress-steps {
                grid-template-columns: repeat(2, 1fr);
            }

            .result-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .header-content {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <h1 class="scenario-title">场景9：供应商发票批量验真与入账 (OCR)</h1>
            <span class="scenario-badge">智能应用模块</span>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="container">
        <!-- 进度跟踪 -->
        <div class="progress-container">
            <div class="progress-header">
                <h2 class="progress-title">任务进度</h2>
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="step" id="step1">
                    <div class="step-circle">1</div>
                    <div class="step-label">上传发票图片</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div class="step-label">OCR识别信息</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div class="step-label">发票验真</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div class="step-label">生成会计分录</div>
                </div>
            </div>
        </div>

        <!-- 步骤1: 发票上传 -->
        <div class="operation-panel" id="uploadPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                步骤1: 上传发票图片
            </h3>
            <div class="upload-area" id="uploadArea">
                <svg class="upload-icon" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <div class="upload-text">点击或拖拽上传发票图片</div>
                <div class="upload-hint">支持 JPG、PNG、PDF 格式，最大 10MB</div>
                <input type="file" id="fileInput" class="hidden" accept=".jpg,.jpeg,.png,.pdf" multiple>
            </div>
            <div class="file-list hidden" id="fileList"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="startOCRBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M12,6.5A2.5,2.5 0 0,1 14.5,4A2.5,2.5 0 0,1 17,6.5C17,8.24 15.16,10.5 12,16.5C8.84,10.5 7,8.24 7,6.5A2.5,2.5 0 0,1 9.5,4A2.5,2.5 0 0,1 12,6.5Z" />
                    </svg>
                    开始OCR识别
                </button>
            </div>
        </div>

        <!-- 步骤2: OCR识别结果 -->
        <div class="operation-panel hidden" id="ocrPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A9,9 0 0,0 21,12A9,9 0 0,0 12,3M12,5A7,7 0 0,1 19,12A7,7 0 0,1 12,19A7,7 0 0,1 5,12A7,7 0 0,1 12,5M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z" />
                </svg>
                步骤2: OCR识别结果
            </h3>
            <div class="ocr-results" id="ocrResults"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="verifyInvoicesBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                    </svg>
                    验证发票真伪
                </button>
            </div>
        </div>

        <!-- 步骤3: 发票验真结果 -->
        <div class="operation-panel hidden" id="verificationPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z" />
                </svg>
                步骤3: 发票验证结果
            </h3>
            <div id="verificationResults"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="generateEntriesBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M17,12H12V17H10V12H7L12,7L17,12Z" />
                    </svg>
                    生成会计分录
                </button>
            </div>
        </div>

        <!-- 步骤4: 会计分录生成 -->
        <div class="operation-panel hidden" id="entryPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z" />
                </svg>
                步骤4: 生成的会计分录
            </h3>
            <div id="accountingEntries"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="completeTaskBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                    </svg>
                    完成任务
                </button>
                <button class="btn btn-secondary" id="resetTaskBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                    </svg>
                    重置任务
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const state = {
            currentStep: 1,
            uploadedFiles: [],
            ocrResults: [],
            verificationResults: [],
            accountingEntries: [],
            completedSteps: [],
            startTime: Date.now()
        };

        // 模拟OCR数据
        const mockOCRData = [
            {
                filename: "invoice_001.jpg",
                confidence: 0.95,
                data: {
                    invoiceNumber: "********",
                    date: "2024-08-04",
                    sellerName: "北京科技有限公司",
                    sellerTaxId: "91110108MA01234567",
                    buyerName: "上海商贸有限公司",  
                    buyerTaxId: "91310115MA0987654321",
                    totalAmount: 11600.00,
                    taxAmount: 1600.00,
                    amountWithoutTax: 10000.00,
                    taxRate: 0.16,
                    items: [
                        { name: "办公用品", amount: 5000.00, taxAmount: 800.00 },
                        { name: "电脑设备", amount: 5000.00, taxAmount: 800.00 }
                    ]
                }
            },
            {
                filename: "invoice_002.jpg", 
                confidence: 0.88,
                data: {
                    invoiceNumber: "87654321",
                    date: "2024-08-03", 
                    sellerName: "深圳制造有限公司",
                    sellerTaxId: "91440300MA0567890123",
                    buyerName: "上海商贸有限公司",
                    buyerTaxId: "91310115MA0987654321",
                    totalAmount: 5800.00,
                    taxAmount: 800.00,
                    amountWithoutTax: 5000.00,
                    taxRate: 0.16,
                    items: [
                        { name: "原材料", amount: 5000.00, taxAmount: 800.00 }
                    ]
                }
            }
        ];

        // DOM元素引用
        const elements = {
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            fileList: document.getElementById('fileList'),
            startOCRBtn: document.getElementById('startOCRBtn'),
            ocrPanel: document.getElementById('ocrPanel'),
            ocrResults: document.getElementById('ocrResults'),
            verifyInvoicesBtn: document.getElementById('verifyInvoicesBtn'),
            verificationPanel: document.getElementById('verificationPanel'),
            verificationResults: document.getElementById('verificationResults'),
            generateEntriesBtn: document.getElementById('generateEntriesBtn'),
            entryPanel: document.getElementById('entryPanel'),
            accountingEntries: document.getElementById('accountingEntries'),
            completeTaskBtn: document.getElementById('completeTaskBtn'),
            resetTaskBtn: document.getElementById('resetTaskBtn'),
            progressFill: document.getElementById('progressFill'),
            progressPercentage: document.getElementById('progressPercentage')
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateStepDisplay();
            showAlert('info', '请上传发票图片开始OCR识别任务');
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            // 文件上传
            elements.uploadArea.addEventListener('click', () => elements.fileInput.click());
            elements.uploadArea.addEventListener('dragover', handleDragOver);
            elements.uploadArea.addEventListener('drop', handleFileDrop);
            elements.fileInput.addEventListener('change', handleFileSelect);

            // 按钮事件
            elements.startOCRBtn.addEventListener('click', startOCR);
            elements.verifyInvoicesBtn.addEventListener('click', verifyInvoices);
            elements.generateEntriesBtn.addEventListener('click', generateAccountingEntries);
            elements.completeTaskBtn.addEventListener('click', completeTask);
            elements.resetTaskBtn.addEventListener('click', resetTask);
        }

        // 文件拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            elements.uploadArea.classList.add('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            elements.uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        }

        // 文件处理
        function handleFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    state.uploadedFiles.push(file);
                    displayFile(file);
                }
            });

            if (state.uploadedFiles.length > 0) {
                elements.startOCRBtn.disabled = false;
                elements.fileList.classList.remove('hidden');
            }
        }

        function validateFile(file) {
            const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                showAlert('error', '不支持的文件格式，请上传 JPG、PNG 或 PDF 文件');
                return false;
            }

            if (file.size > maxSize) {
                showAlert('error', '文件大小超过限制，请选择小于 10MB 的文件');
                return false;
            }

            return true;
        }

        function displayFile(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <svg class="file-icon" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-danger btn-sm" onclick="removeFile('${file.name}')">删除</button>
                </div>
            `;
            elements.fileList.appendChild(fileItem);
        }

        function removeFile(fileName) {
            state.uploadedFiles = state.uploadedFiles.filter(file => file.name !== fileName);
            if (state.uploadedFiles.length === 0) {
                elements.startOCRBtn.disabled = true;
                elements.fileList.classList.add('hidden');
            }
            // 重新渲染文件列表
            elements.fileList.innerHTML = '';
            state.uploadedFiles.forEach(displayFile);
        }

        // OCR识别
        function startOCR() {
            elements.startOCRBtn.disabled = true;
            elements.startOCRBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    OCR识别中...
                </div>
            `;

            // 模拟OCR处理
            setTimeout(() => {
                state.ocrResults = mockOCRData.slice(0, state.uploadedFiles.length);
                state.completedSteps.push('upload');
                displayOCRResults();
                nextStep();
                showAlert('success', `成功识别 ${state.ocrResults.length} 张发票信息`);
            }, 3000);
        }

        function displayOCRResults() {
            elements.ocrResults.innerHTML = '';
            
            state.ocrResults.forEach((result, index) => {
                const confidenceClass = result.confidence >= 0.9 ? 'confidence-high' : 
                                      result.confidence >= 0.7 ? 'confidence-medium' : 'confidence-low';
                
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <div class="invoice-title">发票 ${index + 1}: ${result.filename}</div>
                        <div class="confidence-badge ${confidenceClass}">
                            识别置信度: ${(result.confidence * 100).toFixed(1)}%
                        </div>
                    </div>
                    <div class="result-grid">
                        <div class="result-field">
                            <div class="field-label">发票号码</div>
                            <div class="field-value">${result.data.invoiceNumber}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">开票日期</div>
                            <div class="field-value">${result.data.date}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">销售方名称</div>
                            <div class="field-value">${result.data.sellerName}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">销售方税号</div>
                            <div class="field-value">${result.data.sellerTaxId}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">购买方名称</div>
                            <div class="field-value">${result.data.buyerName}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">价税合计</div>
                            <div class="field-value">¥${result.data.totalAmount.toFixed(2)}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">不含税金额</div>
                            <div class="field-value">¥${result.data.amountWithoutTax.toFixed(2)}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">税额</div>
                            <div class="field-value">¥${result.data.taxAmount.toFixed(2)}</div>
                        </div>
                    </div>
                `;
                elements.ocrResults.appendChild(resultCard);
            });

            elements.verifyInvoicesBtn.disabled = false;
            elements.ocrPanel.classList.remove('hidden');
        }

        // 发票验证
        function verifyInvoices() {
            elements.verifyInvoicesBtn.disabled = true;
            elements.verifyInvoicesBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    验证中...
                </div>
            `;

            // 模拟验证过程
            setTimeout(() => {
                state.verificationResults = state.ocrResults.map((result, index) => ({
                    ...result,
                    isValid: Math.random() > 0.2, // 80%概率通过验证
                    verificationCode: `V${Date.now()}${index}`,
                    verificationTime: new Date().toISOString()
                }));

                state.completedSteps.push('ocr');
                displayVerificationResults();
                nextStep();
                
                const validCount = state.verificationResults.filter(r => r.isValid).length;
                showAlert('success', `验证完成：${validCount}/${state.verificationResults.length} 张发票验证通过`);
            }, 2500);
        }

        function displayVerificationResults() {
            elements.verificationResults.innerHTML = '';
            
            state.verificationResults.forEach((result, index) => {
                const statusClass = result.isValid ? 'status-success' : 'status-error';
                const statusText = result.isValid ? '验证通过' : '验证失败';
                const statusIcon = result.isValid ? '✓' : '✗';
                
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <div class="invoice-title">发票 ${index + 1}: ${result.data.invoiceNumber}</div>
                        <div class="confidence-badge ${result.isValid ? 'confidence-high' : 'confidence-low'}">
                            ${statusIcon} ${statusText}
                        </div>
                    </div>
                    <div class="verification-status ${statusClass}">
                        <strong>${statusText}</strong>
                        ${result.isValid ? `- 验证码: ${result.verificationCode}` : '- 请检查发票信息'}
                    </div>
                    <div class="result-grid">
                        <div class="result-field">
                            <div class="field-label">销售方</div>
                            <div class="field-value">${result.data.sellerName}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">金额</div>
                            <div class="field-value">¥${result.data.totalAmount.toFixed(2)}</div>
                        </div>
                        <div class="result-field">
                            <div class="field-label">验证时间</div>
                            <div class="field-value">${new Date(result.verificationTime).toLocaleString()}</div>
                        </div>
                    </div>
                `;
                elements.verificationResults.appendChild(resultCard);
            });

            elements.generateEntriesBtn.disabled = false;
            elements.verificationPanel.classList.remove('hidden');
        }

        // 生成会计分录
        function generateAccountingEntries() {
            const validInvoices = state.verificationResults.filter(r => r.isValid);
            
            if (validInvoices.length === 0) {
                showAlert('warning', '没有通过验证的发票，无法生成会计分录');
                return;
            }

            state.accountingEntries = validInvoices.map((invoice, index) => ({
                invoiceNumber: invoice.data.invoiceNumber,
                entries: [
                    {
                        account: '应付账款',
                        accountCode: '2202',
                        debit: invoice.data.totalAmount,
                        credit: 0,
                        description: `采购${invoice.data.sellerName}商品`
                    },
                    {
                        account: '原材料/库存商品',
                        accountCode: '1403',
                        debit: 0,
                        credit: invoice.data.amountWithoutTax,
                        description: `入库${invoice.data.sellerName}商品`
                    },
                    {
                        account: '应交税费-应交增值税(进项税额)',
                        accountCode: '2221001',
                        debit: 0,
                        credit: invoice.data.taxAmount,
                        description: `进项税额`
                    }
                ],
                totalDebit: invoice.data.totalAmount,
                totalCredit: invoice.data.totalAmount
            }));

            state.completedSteps.push('verification');
            displayAccountingEntries();
            nextStep();
            showAlert('success', `成功生成 ${state.accountingEntries.length} 笔会计分录`);
        }

        function displayAccountingEntries() {
            elements.accountingEntries.innerHTML = '';
            
            state.accountingEntries.forEach((entry, index) => {
                const entryCard = document.createElement('div');
                entryCard.className = 'result-card';
                entryCard.innerHTML = `
                    <div class="result-header">
                        <div class="invoice-title">会计分录 ${index + 1} - 发票号: ${entry.invoiceNumber}</div>
                    </div>
                    <div class="accounting-entry">
                        <table class="entry-table">
                            <thead>
                                <tr>
                                    <th>科目名称</th>
                                    <th>科目代码</th>
                                    <th>借方金额</th>
                                    <th>贷方金额</th>
                                    <th>摘要</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${entry.entries.map(e => `
                                    <tr>
                                        <td>${e.account}</td>
                                        <td>${e.accountCode}</td>
                                        <td>${e.debit ? '¥' + e.debit.toFixed(2) : ''}</td>
                                        <td>${e.credit ? '¥' + e.credit.toFixed(2) : ''}</td>
                                        <td>${e.description}</td>
                                    </tr>
                                `).join('')}
                                <tr style="font-weight: bold; background: #F8FAFC;">
                                    <td colspan="2">合计</td>
                                    <td>¥${entry.totalDebit.toFixed(2)}</td>
                                    <td>¥${entry.totalCredit.toFixed(2)}</td>
                                    <td>${entry.totalDebit === entry.totalCredit ? '借贷平衡' : '不平衡'}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
                elements.accountingEntries.appendChild(entryCard);
            });

            elements.completeTaskBtn.disabled = false;
            elements.entryPanel.classList.remove('hidden');
        }

        // 完成任务
        function completeTask() {
            state.completedSteps.push('entries');
            updateStepDisplay();
            
            // 准备提交数据
            const taskData = {
                completedSteps: state.completedSteps,
                uploadedFileCount: state.uploadedFiles.length,
                ocrResultCount: state.ocrResults.length,
                verifiedInvoiceCount: state.verificationResults.filter(r => r.isValid).length,
                generatedEntriesCount: state.accountingEntries.length,
                totalInvoiceAmount: state.verificationResults
                    .filter(r => r.isValid)
                    .reduce((sum, r) => sum + r.data.totalAmount, 0),
                averageConfidence: state.ocrResults
                    .reduce((sum, r) => sum + r.confidence, 0) / state.ocrResults.length,
                timeSpent: Math.floor((Date.now() - state.startTime) / 1000),
                taskCompleted: true
            };

            // 提交验证
            submitTaskValidation(taskData);
        }

        // 提交任务验证
        function submitTaskValidation(taskData) {
            // 模拟API调用
            fetch('/api/scenarios/9/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(taskData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.valid) {
                    showAlert('success', `🎉 ${data.feedback} 得分: ${data.score}分`);
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', data.feedback || '任务验证未通过，请检查操作步骤');
                }
            })
            .catch(error => {
                console.error('任务验证失败:', error);
                // 本地验证逻辑
                if (validateLocalTask(taskData)) {
                    showAlert('success', '🎉 恭喜！您已成功完成OCR发票验证任务！');
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', '任务验证未通过，请确保完成所有步骤');
                }
            });
        }

        // 本地任务验证
        function validateLocalTask(data) {
            const requiredSteps = ['upload', 'ocr', 'verification', 'entries'];
            const stepsCompleted = requiredSteps.every(step => 
                data.completedSteps && data.completedSteps.includes(step)
            );
            
            return stepsCompleted && 
                   data.uploadedFileCount >= 2 && 
                   data.verifiedInvoiceCount >= 1 && 
                   data.generatedEntriesCount >= 1 &&
                   data.averageConfidence >= 0.7;
        }

        // 重置任务
        function resetTask() {
            if (confirm('确定要重置任务吗？所有进度将丢失。')) {
                location.reload();
            }
        }

        // 步骤管理
        function nextStep() {
            if (state.currentStep < 4) {
                state.currentStep++;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // 更新进度条
            const progress = (state.currentStep - 1) * 25;
            elements.progressFill.style.width = `${progress}%`;
            elements.progressPercentage.textContent = `${progress}%`;

            // 更新步骤状态
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                
                if (i < state.currentStep) {
                    step.classList.add('completed');
                } else if (i === state.currentStep) {
                    step.classList.add('active');
                }
            }
        }

        // 显示提示消息
        function showAlert(type, message) {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                </svg>
                ${message}
            `;

            document.querySelector('.container').insertBefore(alert, document.querySelector('.progress-container'));

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // 工具函数：格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 工具函数：生成唯一ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }
    </script>
</body>
</html>