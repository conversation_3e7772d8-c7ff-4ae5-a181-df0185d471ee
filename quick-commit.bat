@echo off
chcp 65001 > nul
echo ==========================================
echo      RPA实训平台 - 快速提交到GitHub
echo ==========================================
echo.

cd /d "D:\G盘（软件）\cursor开发文件\claude-RPA实训平台3.0"

echo 🔍 检查文件变化...
git status
echo.

echo 📦 添加所有文件到暂存区...
git add .
echo.

echo 💾 提交更改...
git commit -m "feat: 新增第三模块智能财务应用场景

🎯 新增功能:
- ✅ 场景9: 合同印花税自动计算系统
- ✅ 场景10: 差旅费用智能审核系统  
- ✅ 场景11: 发票OCR识别验证系统
- ✅ 场景12: 银行对账单自动核对系统

🏗️ 技术优化:
- 统一Twilight主题设计
- 4步骤标准化工作流
- 完善后端API支持
- 优化用户体验

📊 项目进度: 第三模块完成，总计12个场景全部开发完毕

🤖 Generated with Claude Code"

echo.
echo 🚀 推送到GitHub...
git push origin main

echo.
if %errorlevel% equ 0 (
    echo ✅ 提交成功！
    echo 🌐 访问仓库: https://github.com/chanwarmsun/RPA
    echo 🎉 恭喜！RPA实训平台开发完成！
) else (
    echo ❌ 推送失败，请检查网络连接或认证信息
)

echo.
echo 按任意键退出...
pause > nul
